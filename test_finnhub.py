import os
import json
import finnhub
from dotenv import load_dotenv
from datetime import datetime

# ✅ 加载 .env 文件
load_dotenv()
api_key = os.getenv("FINNHUB_API_KEY")

if not api_key:
    raise ValueError("❌ 没有找到 FINNHUB_API_KEY，请确保 .env 文件设置正确。")

# ✅ 初始化客户端
finnhub_client = finnhub.Client(api_key=api_key)

# ✅ 设置参数
symbol = 'AAPL'
date_from = "2025-05-01"
date_to = "2025-05-01"

# ✅ 创建输出目录
output_dir = "finnhub_news_by_date"
os.makedirs(output_dir, exist_ok=True)

# ✅ 获取公司新闻
try:
    news_items = finnhub_client.company_news(symbol, _from=date_from, to=date_to)
    print(f"✅ 成功获取到 {len(news_items)} 条新闻")
except Exception as e:
    print("❌ 获取新闻失败：", str(e))
    exit(1)

# ✅ 处理并保存新闻数据
for item in news_items:
    # 转换时间戳为 YYYY-MM-DD
    timestamp = item.get("datetime")
    if not timestamp:
        continue
    date_str = datetime.utcfromtimestamp(timestamp).strftime("%Y-%m-%d")

    # 保存为 JSONL，每日一个文件
    file_path = os.path.join(output_dir, f"{date_str}.jsonl")
    with open(file_path, "a", encoding="utf-8") as f:
        f.write(json.dumps(item, ensure_ascii=False) + "\n")

print(f"📁 新闻已按日期保存至：{output_dir}")
