#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按日期组织 Alpha News 数据

此脚本将 processed_alpha_news.json 文件中的新闻数据按日期重新分类，
为每个日期创建单独的 JSON 文件，保持与新闻代理的兼容性。

作者: AI Assistant
日期: 2025-06-15
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any
from collections import defaultdict


class DateOrganizer:
    """按日期组织新闻数据的处理器"""
    
    def __init__(self, input_file: str = "processed_alpha_news.json", output_dir: str = "processed_by_date"):
        """
        初始化组织器
        
        Args:
            input_file: 输入的JSON文件路径
            output_dir: 输出目录路径
        """
        self.input_file = input_file
        self.output_dir = output_dir
        self.data = None
        self.date_groups = defaultdict(list)
        self.stats = {
            'total_articles': 0,
            'total_dates': 0,
            'articles_by_date': {},
            'date_range': {'earliest': None, 'latest': None},
            'processing_errors': []
        }
    
    def load_input_data(self) -> bool:
        """
        加载输入数据
        
        Returns:
            bool: 加载是否成功
        """
        try:
            if not os.path.exists(self.input_file):
                print(f"错误：输入文件 '{self.input_file}' 不存在")
                return False
            
            with open(self.input_file, 'r', encoding='utf-8') as file:
                self.data = json.load(file)
            
            print(f"✅ 成功加载输入文件: {self.input_file}")
            
            # 验证数据结构
            if 'articles' not in self.data:
                print("错误：输入文件缺少 'articles' 字段")
                return False
            
            self.stats['total_articles'] = len(self.data['articles'])
            print(f"📊 总文章数: {self.stats['total_articles']}")
            
            return True
            
        except json.JSONDecodeError as e:
            print(f"错误：JSON解析失败: {e}")
            return False
        except Exception as e:
            print(f"错误：加载文件时发生错误: {e}")
            return False
    
    def create_output_directory(self) -> bool:
        """
        创建输出目录
        
        Returns:
            bool: 创建是否成功
        """
        try:
            if not os.path.exists(self.output_dir):
                os.makedirs(self.output_dir)
                print(f"📁 创建输出目录: {self.output_dir}")
            else:
                print(f"📁 使用现有输出目录: {self.output_dir}")
            
            return True
            
        except Exception as e:
            print(f"错误：创建输出目录失败: {e}")
            return False
    
    def parse_date_from_timestamp(self, timestamp: str) -> str:
        """
        从时间戳中提取日期
        
        Args:
            timestamp: 时间戳字符串 (格式: YYYYMMDDTHHMMSS)
            
        Returns:
            str: 日期字符串 (格式: YYYY-MM-DD)
        """
        try:
            # 处理格式如 "20250506T180500"
            if 'T' in timestamp:
                date_part = timestamp.split('T')[0]
            else:
                date_part = timestamp[:8]  # 取前8位作为日期
            
            # 转换为 YYYY-MM-DD 格式
            if len(date_part) == 8:
                year = date_part[:4]
                month = date_part[4:6]
                day = date_part[6:8]
                return f"{year}-{month}-{day}"
            else:
                raise ValueError(f"无效的日期格式: {timestamp}")
                
        except Exception as e:
            self.stats['processing_errors'].append(f"日期解析错误 '{timestamp}': {e}")
            return "unknown-date"
    
    def group_articles_by_date(self) -> bool:
        """
        按日期分组文章
        
        Returns:
            bool: 分组是否成功
        """
        print("\n🔄 开始按日期分组文章...")
        
        for i, article in enumerate(self.data['articles']):
            try:
                # 获取时间戳
                timestamp = article.get('time_published', '')
                if not timestamp:
                    self.stats['processing_errors'].append(f"文章 {i+1} 缺少 time_published 字段")
                    continue
                
                # 解析日期
                date_str = self.parse_date_from_timestamp(timestamp)
                
                # 添加到对应日期组
                self.date_groups[date_str].append(article)
                
                # 更新统计信息
                if date_str not in self.stats['articles_by_date']:
                    self.stats['articles_by_date'][date_str] = 0
                self.stats['articles_by_date'][date_str] += 1
                
                # 更新日期范围
                if date_str != "unknown-date":
                    if not self.stats['date_range']['earliest'] or date_str < self.stats['date_range']['earliest']:
                        self.stats['date_range']['earliest'] = date_str
                    if not self.stats['date_range']['latest'] or date_str > self.stats['date_range']['latest']:
                        self.stats['date_range']['latest'] = date_str
                
            except Exception as e:
                self.stats['processing_errors'].append(f"处理文章 {i+1} 时发生错误: {e}")
        
        self.stats['total_dates'] = len(self.date_groups)
        print(f"📅 共分组到 {self.stats['total_dates']} 个不同日期")
        
        return True
    
    def create_date_file(self, date_str: str, articles: List[Dict[str, Any]]) -> bool:
        """
        为指定日期创建JSON文件
        
        Args:
            date_str: 日期字符串
            articles: 该日期的文章列表
            
        Returns:
            bool: 创建是否成功
        """
        try:
            # 创建文件名
            filename = f"alpha_news_{date_str}.json"
            filepath = os.path.join(self.output_dir, filename)
            
            # 创建文件数据结构
            file_data = {
                "metadata": {
                    "date": date_str,
                    "processing_date": datetime.now().isoformat(),
                    "total_articles": len(articles),
                    "extracted_fields": [
                        "title",
                        "url",
                        "time_published",
                        "authors",
                        "summary",
                        "overall_sentiment_score",
                        "overall_sentiment_label"
                    ],
                    "description": f"Alpha news data for {date_str} - compatible with factual and subjective news agents",
                    "source_file": self.input_file
                },
                "articles": articles
            }
            
            # 保存文件
            with open(filepath, 'w', encoding='utf-8') as file:
                json.dump(file_data, file, ensure_ascii=False, indent=2)
            
            print(f"  ✅ {filename} - {len(articles)} 篇文章")
            return True
            
        except Exception as e:
            print(f"  ❌ 创建文件 {date_str} 失败: {e}")
            self.stats['processing_errors'].append(f"创建文件 {date_str} 失败: {e}")
            return False
    
    def save_all_date_files(self) -> bool:
        """
        保存所有日期文件
        
        Returns:
            bool: 保存是否成功
        """
        print(f"\n💾 开始创建 {len(self.date_groups)} 个日期文件...")
        
        success_count = 0
        
        # 按日期排序处理
        sorted_dates = sorted(self.date_groups.keys())
        
        for date_str in sorted_dates:
            articles = self.date_groups[date_str]
            if self.create_date_file(date_str, articles):
                success_count += 1
        
        print(f"\n📁 成功创建 {success_count}/{len(self.date_groups)} 个文件")
        
        return success_count == len(self.date_groups)
    
    def print_statistics(self):
        """打印处理统计信息"""
        print("\n" + "=" * 60)
        print("按日期组织统计信息")
        print("=" * 60)
        
        print(f"输入文件: {self.input_file}")
        print(f"输出目录: {self.output_dir}")
        print(f"总文章数: {self.stats['total_articles']}")
        print(f"日期数量: {self.stats['total_dates']}")
        
        if self.stats['date_range']['earliest']:
            print(f"日期范围: {self.stats['date_range']['earliest']} 到 {self.stats['date_range']['latest']}")
        
        print(f"\n每日文章分布:")
        sorted_dates = sorted(self.stats['articles_by_date'].keys())
        for date_str in sorted_dates:
            count = self.stats['articles_by_date'][date_str]
            print(f"  {date_str}: {count} 篇")
        
        if self.stats['processing_errors']:
            print(f"\n⚠️  处理错误 ({len(self.stats['processing_errors'])} 个):")
            for error in self.stats['processing_errors'][:10]:  # 只显示前10个错误
                print(f"  • {error}")
            if len(self.stats['processing_errors']) > 10:
                print(f"  ... 还有 {len(self.stats['processing_errors']) - 10} 个错误")
        
        print("=" * 60)
    
    def run(self) -> bool:
        """
        运行完整的组织流程
        
        Returns:
            bool: 处理是否成功
        """
        print("Alpha News 按日期组织器")
        print("=" * 60)
        
        # 加载输入数据
        if not self.load_input_data():
            return False
        
        # 创建输出目录
        if not self.create_output_directory():
            return False
        
        # 按日期分组
        if not self.group_articles_by_date():
            return False
        
        # 保存所有日期文件
        if not self.save_all_date_files():
            return False
        
        # 打印统计信息
        self.print_statistics()
        
        return True


def main():
    """主函数"""
    organizer = DateOrganizer()
    
    success = organizer.run()
    
    if success:
        print("\n🎉 按日期组织完成！")
        print(f"📂 所有文件已保存到: {organizer.output_dir}/")
        print("🤖 数据已准备好供事实性和主观性新闻代理使用")
    else:
        print("\n❌ 按日期组织失败！")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
