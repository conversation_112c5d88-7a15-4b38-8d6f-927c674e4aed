#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
News API 数据处理脚本

此脚本用于处理 news_api_news 文件夹中的所有 JSONL 文件，
提取指定字段并按日期重新组织为 JSON 格式。

作者: AI Assistant
日期: 2025-06-15
"""

import json
import os
import glob
from datetime import datetime
from typing import Dict, List, Any
from collections import defaultdict
import re


class NewsAPIProcessor:
    """News API 数据处理器"""
    
    def __init__(self, input_dir: str = "news_api_news", output_dir: str = "news_api_by_date"):
        """
        初始化处理器
        
        Args:
            input_dir: 输入目录路径
            output_dir: 输出目录路径
        """
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.required_fields = [
            'author',
            'title',
            'description',
            'url',
            'publishedAt'
        ]
        self.date_groups = defaultdict(list)
        self.stats = {
            'total_files': 0,
            'total_articles': 0,
            'successful_extractions': 0,
            'failed_extractions': 0,
            'missing_fields': {},
            'date_range': {'earliest': None, 'latest': None},
            'articles_by_date': {},
            'processing_errors': []
        }
    
    def validate_input_directory(self) -> bool:
        """
        验证输入目录是否存在
        
        Returns:
            bool: 目录是否存在
        """
        if not os.path.exists(self.input_dir):
            print(f"错误：输入目录 '{self.input_dir}' 不存在")
            return False
        
        jsonl_files = glob.glob(os.path.join(self.input_dir, "*.jsonl"))
        if not jsonl_files:
            print(f"错误：在目录 '{self.input_dir}' 中未找到 .jsonl 文件")
            return False
        
        self.stats['total_files'] = len(jsonl_files)
        print(f"找到 {len(jsonl_files)} 个 JSONL 文件")
        return True
    
    def create_output_directory(self) -> bool:
        """
        创建输出目录
        
        Returns:
            bool: 创建是否成功
        """
        try:
            if not os.path.exists(self.output_dir):
                os.makedirs(self.output_dir)
                print(f"📁 创建输出目录: {self.output_dir}")
            else:
                print(f"📁 使用现有输出目录: {self.output_dir}")
            
            return True
            
        except Exception as e:
            print(f"错误：创建输出目录失败: {e}")
            return False
    
    def parse_date_from_published_at(self, published_at: str) -> str:
        """
        从 publishedAt 字段中提取日期
        
        Args:
            published_at: 发布时间字符串 (ISO 8601 格式)
            
        Returns:
            str: 日期字符串 (格式: YYYY-MM-DD)
        """
        try:
            # 处理 ISO 8601 格式，如 "2025-05-16T20:45:41Z"
            if 'T' in published_at:
                date_part = published_at.split('T')[0]
                # 验证日期格式 YYYY-MM-DD
                if re.match(r'^\d{4}-\d{2}-\d{2}$', date_part):
                    return date_part
            
            # 尝试其他可能的格式
            # 如果是其他格式，尝试解析
            try:
                dt = datetime.fromisoformat(published_at.replace('Z', '+00:00'))
                return dt.strftime('%Y-%m-%d')
            except:
                pass
            
            raise ValueError(f"无法解析的日期格式: {published_at}")
                
        except Exception as e:
            self.stats['processing_errors'].append(f"日期解析错误 '{published_at}': {e}")
            return "unknown-date"
    
    def extract_required_fields(self, article_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        从文章数据中提取所需字段
        
        Args:
            article_data: 原始文章数据
            
        Returns:
            Dict: 提取的字段数据和缺失字段列表
        """
        extracted = {}
        missing_fields = []
        
        for field in self.required_fields:
            if field in article_data and article_data[field] is not None:
                extracted[field] = article_data[field]
            else:
                extracted[field] = None
                missing_fields.append(field)
                
                # 统计缺失字段
                if field not in self.stats['missing_fields']:
                    self.stats['missing_fields'][field] = 0
                self.stats['missing_fields'][field] += 1
        
        return extracted, missing_fields
    
    def process_jsonl_file(self, file_path: str) -> bool:
        """
        处理单个 JSONL 文件
        
        Args:
            file_path: JSONL 文件路径
            
        Returns:
            bool: 处理是否成功
        """
        file_name = os.path.basename(file_path)
        file_articles = 0
        file_errors = 0
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                line_number = 0
                for line in file:
                    line_number += 1
                    line = line.strip()
                    
                    if not line:  # 跳过空行
                        continue
                    
                    try:
                        # 解析 JSON 数据
                        article_data = json.loads(line)
                        self.stats['total_articles'] += 1
                        file_articles += 1
                        
                        # 提取所需字段
                        extracted_data, missing_fields = self.extract_required_fields(article_data)
                        
                        # 解析日期
                        published_at = extracted_data.get('publishedAt', '')
                        if published_at:
                            date_str = self.parse_date_from_published_at(published_at)
                        else:
                            date_str = "unknown-date"
                            self.stats['processing_errors'].append(f"文件 {file_name} 第 {line_number} 行缺少 publishedAt 字段")
                        
                        # 添加元数据
                        extracted_data['source_file'] = file_name
                        extracted_data['line_number'] = line_number
                        
                        # 添加到对应日期组
                        self.date_groups[date_str].append(extracted_data)
                        
                        # 更新统计信息
                        if date_str not in self.stats['articles_by_date']:
                            self.stats['articles_by_date'][date_str] = 0
                        self.stats['articles_by_date'][date_str] += 1
                        
                        # 更新日期范围
                        if date_str != "unknown-date":
                            if not self.stats['date_range']['earliest'] or date_str < self.stats['date_range']['earliest']:
                                self.stats['date_range']['earliest'] = date_str
                            if not self.stats['date_range']['latest'] or date_str > self.stats['date_range']['latest']:
                                self.stats['date_range']['latest'] = date_str
                        
                        self.stats['successful_extractions'] += 1
                        
                        if missing_fields:
                            print(f"警告：文件 {file_name} 第 {line_number} 行缺少字段: {missing_fields}")
                    
                    except json.JSONDecodeError as e:
                        print(f"错误：文件 {file_name} 第 {line_number} 行 JSON 解析失败: {e}")
                        self.stats['failed_extractions'] += 1
                        file_errors += 1
                    
                    except Exception as e:
                        print(f"错误：处理文件 {file_name} 第 {line_number} 行时发生未知错误: {e}")
                        self.stats['failed_extractions'] += 1
                        file_errors += 1
        
        except FileNotFoundError:
            print(f"错误：文件 {file_path} 未找到")
            return False
        except Exception as e:
            print(f"错误：读取文件 {file_path} 时发生错误: {e}")
            return False
        
        print(f"处理完成：{file_name} - 提取了 {file_articles} 篇文章，{file_errors} 个错误")
        return True
    
    def process_all_files(self) -> bool:
        """
        处理所有 JSONL 文件
        
        Returns:
            bool: 处理是否成功
        """
        if not self.validate_input_directory():
            return False
        
        # 获取所有 JSONL 文件
        jsonl_files = glob.glob(os.path.join(self.input_dir, "*.jsonl"))
        jsonl_files.sort()  # 按文件名排序
        
        print(f"\n开始处理 {len(jsonl_files)} 个 JSONL 文件...")
        print("=" * 60)
        
        # 处理每个文件
        success_count = 0
        for file_path in jsonl_files:
            if self.process_jsonl_file(file_path):
                success_count += 1
        
        print("=" * 60)
        print(f"文件处理完成：{success_count}/{len(jsonl_files)} 个文件成功处理")
        print(f"共分组到 {len(self.date_groups)} 个不同日期")
        
        return success_count > 0

    def create_date_file(self, date_str: str, articles: List[Dict[str, Any]]) -> bool:
        """
        为指定日期创建JSON文件

        Args:
            date_str: 日期字符串
            articles: 该日期的文章列表

        Returns:
            bool: 创建是否成功
        """
        try:
            # 创建文件名
            filename = f"news_api_{date_str}.json"
            filepath = os.path.join(self.output_dir, filename)

            # 创建文件数据结构
            file_data = {
                "metadata": {
                    "date": date_str,
                    "processing_date": datetime.now().isoformat(),
                    "total_articles": len(articles),
                    "extracted_fields": self.required_fields,
                    "description": f"News API data for {date_str} - processed from JSONL format",
                    "source_directory": self.input_dir
                },
                "articles": articles
            }

            # 保存文件
            with open(filepath, 'w', encoding='utf-8') as file:
                json.dump(file_data, file, ensure_ascii=False, indent=2)

            print(f"  ✅ {filename} - {len(articles)} 篇文章")
            return True

        except Exception as e:
            print(f"  ❌ 创建文件 {date_str} 失败: {e}")
            self.stats['processing_errors'].append(f"创建文件 {date_str} 失败: {e}")
            return False

    def save_all_date_files(self) -> bool:
        """
        保存所有日期文件

        Returns:
            bool: 保存是否成功
        """
        if not self.create_output_directory():
            return False

        print(f"\n💾 开始创建 {len(self.date_groups)} 个日期文件...")

        success_count = 0

        # 按日期排序处理
        sorted_dates = sorted(self.date_groups.keys())

        for date_str in sorted_dates:
            articles = self.date_groups[date_str]
            if self.create_date_file(date_str, articles):
                success_count += 1

        print(f"\n📁 成功创建 {success_count}/{len(self.date_groups)} 个文件")

        return success_count == len(self.date_groups)

    def print_statistics(self):
        """打印处理统计信息"""
        print("\n" + "=" * 60)
        print("News API 数据处理统计信息")
        print("=" * 60)

        print(f"输入目录: {self.input_dir}")
        print(f"输出目录: {self.output_dir}")
        print(f"总文件数: {self.stats['total_files']}")
        print(f"总文章数: {self.stats['total_articles']}")
        print(f"成功提取: {self.stats['successful_extractions']}")
        print(f"提取失败: {self.stats['failed_extractions']}")
        print(f"日期数量: {len(self.date_groups)}")

        if self.stats['date_range']['earliest']:
            print(f"日期范围: {self.stats['date_range']['earliest']} 到 {self.stats['date_range']['latest']}")

        if self.stats['missing_fields']:
            print(f"\n缺失字段统计:")
            for field, count in self.stats['missing_fields'].items():
                print(f"  {field}: {count} 次")

        print(f"\n每日文章分布:")
        sorted_dates = sorted(self.stats['articles_by_date'].keys())
        for date_str in sorted_dates:
            count = self.stats['articles_by_date'][date_str]
            print(f"  {date_str}: {count} 篇")

        if self.stats['processing_errors']:
            print(f"\n⚠️  处理错误 ({len(self.stats['processing_errors'])} 个):")
            for error in self.stats['processing_errors'][:10]:  # 只显示前10个错误
                print(f"  • {error}")
            if len(self.stats['processing_errors']) > 10:
                print(f"  ... 还有 {len(self.stats['processing_errors']) - 10} 个错误")

        success_rate = (self.stats['successful_extractions'] / self.stats['total_articles'] * 100) if self.stats['total_articles'] > 0 else 0
        print(f"\n成功率: {success_rate:.2f}%")
        print("=" * 60)

    def run(self) -> bool:
        """
        运行完整的处理流程

        Returns:
            bool: 处理是否成功
        """
        print("News API 数据处理器")
        print("=" * 60)
        print(f"输入目录: {self.input_dir}")
        print(f"输出目录: {self.output_dir}")
        print(f"提取字段: {', '.join(self.required_fields)}")
        print("=" * 60)

        # 处理所有文件
        if not self.process_all_files():
            return False

        # 保存所有日期文件
        if not self.save_all_date_files():
            return False

        # 打印统计信息
        self.print_statistics()

        return True


def main():
    """主函数"""
    # 创建处理器实例
    processor = NewsAPIProcessor()

    # 运行处理流程
    success = processor.run()

    if success:
        print("\n✅ News API 数据处理完成！")
        print(f"📁 输出目录: {processor.output_dir}/")
        print("📊 数据已按日期重新组织为 JSON 格式")
    else:
        print("\n❌ News API 数据处理失败！")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
