import os
import json
from datetime import datetime
from newsapi import NewsApiClient
from dotenv import load_dotenv

# ✅ 加载 .env 文件
load_dotenv()
api_key = os.getenv("NEWSAPI_KEY")

if not api_key:
    raise ValueError("❌ 未找到 NEWS_API_KEY，请检查 .env 文件是否正确设置。")

# ✅ 初始化 NewsApiClient
newsapi = NewsApiClient(api_key=api_key)

# ✅ 获取包含 'AAPL' 关键词的新闻
response = newsapi.get_everything(
    q='AAPL',
    from_param='2025-05-15',
    to='2025-05-20',
    language='en',
    sort_by='publishedAt',
    page_size=100  # 最大支持100条
)

# ✅ 创建保存目录
output_dir = "news_api_news"
os.makedirs(output_dir, exist_ok=True)

# ✅ 处理并保存新闻
articles = response.get("articles", [])
print(f"共获取到 {len(articles)} 条新闻，开始按日期分类保存...\n")

for article in articles:
    published_at = article.get("publishedAt")  # 格式如 "2023-11-20T12:34:56Z"
    if not published_at:
        continue

    # 提取日期部分
    date_str = datetime.strptime(published_at[:10], "%Y-%m-%d").strftime("%Y-%m-%d")
    file_path = os.path.join(output_dir, f"{date_str}.jsonl")

    # 写入该条新闻
    with open(file_path, "a", encoding="utf-8") as f:
        f.write(json.dumps(article, ensure_ascii=False) + "\n")

print(f"✅ 新闻保存完成，保存在目录：{output_dir}")
