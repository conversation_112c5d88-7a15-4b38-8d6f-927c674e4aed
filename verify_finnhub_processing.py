#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证 Finnhub News 数据处理结果

此脚本验证 Finnhub News 数据处理的完整性和正确性，
确保所有数据都被正确提取、时间戳转换和组织。

作者: AI Assistant
日期: 2025-06-15
"""

import json
import os
import glob
from datetime import datetime
from typing import Dict, List, Any


class FinnhubVerifier:
    """Finnhub News 数据处理验证器"""
    
    def __init__(self, input_dir: str = "finnhub_news", output_dir: str = "finnhub_by_date"):
        """
        初始化验证器
        
        Args:
            input_dir: 原始JSONL文件目录
            output_dir: 处理后的JSON文件目录
        """
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.required_fields = ['category', 'datetime', 'headline', 'source', 'summary']
        self.verification_results = {
            'input_files': 0,
            'output_files': 0,
            'total_input_articles': 0,
            'total_output_articles': 0,
            'data_integrity': True,
            'field_completeness': {},
            'date_coverage': {},
            'timestamp_validation': {'valid': 0, 'invalid': 0},
            'missing_articles': [],
            'format_issues': [],
            'processing_errors': []
        }
    
    def count_input_articles(self) -> bool:
        """
        统计输入文件中的文章数量
        
        Returns:
            bool: 统计是否成功
        """
        try:
            jsonl_files = glob.glob(os.path.join(self.input_dir, "*.jsonl"))
            self.verification_results['input_files'] = len(jsonl_files)
            
            total_articles = 0
            
            for file_path in jsonl_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as file:
                        for line in file:
                            line = line.strip()
                            if line:  # 非空行
                                try:
                                    json.loads(line)  # 验证JSON格式
                                    total_articles += 1
                                except json.JSONDecodeError:
                                    pass  # 跳过无效行
                except Exception as e:
                    self.verification_results['processing_errors'].append(f"读取文件 {file_path} 失败: {e}")
            
            self.verification_results['total_input_articles'] = total_articles
            print(f"✅ 输入统计: {len(jsonl_files)} 个文件，{total_articles} 篇文章")
            return True
            
        except Exception as e:
            print(f"❌ 输入统计失败: {e}")
            return False
    
    def verify_output_files(self) -> bool:
        """
        验证输出文件
        
        Returns:
            bool: 验证是否成功
        """
        try:
            json_files = glob.glob(os.path.join(self.output_dir, "finnhub_*.json"))
            self.verification_results['output_files'] = len(json_files)
            
            total_articles = 0
            field_stats = {field: {'present': 0, 'missing': 0, 'null': 0} for field in self.required_fields}
            
            for file_path in json_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as file:
                        data = json.load(file)
                    
                    # 验证文件结构
                    if 'metadata' not in data or 'articles' not in data:
                        self.verification_results['format_issues'].append(f"文件 {os.path.basename(file_path)} 缺少必需字段")
                        continue
                    
                    # 提取日期和文章数
                    filename = os.path.basename(file_path)
                    date_str = filename.replace('finnhub_', '').replace('.json', '')
                    articles = data['articles']
                    article_count = len(articles)
                    
                    total_articles += article_count
                    self.verification_results['date_coverage'][date_str] = article_count
                    
                    # 验证字段完整性和时间戳
                    for article in articles:
                        for field in self.required_fields:
                            if field in article:
                                if article[field] is not None:
                                    field_stats[field]['present'] += 1
                                    
                                    # 验证时间戳
                                    if field == 'datetime':
                                        try:
                                            timestamp = article[field]
                                            if isinstance(timestamp, (int, float)):
                                                # 验证时间戳是否合理（应该是2025年的时间戳）
                                                dt = datetime.fromtimestamp(timestamp)
                                                if 2025 <= dt.year <= 2026:
                                                    self.verification_results['timestamp_validation']['valid'] += 1
                                                else:
                                                    self.verification_results['timestamp_validation']['invalid'] += 1
                                            else:
                                                self.verification_results['timestamp_validation']['invalid'] += 1
                                        except Exception:
                                            self.verification_results['timestamp_validation']['invalid'] += 1
                                else:
                                    field_stats[field]['null'] += 1
                            else:
                                field_stats[field]['missing'] += 1
                    
                    print(f"  📅 {date_str}: {article_count} 篇文章")
                    
                except Exception as e:
                    self.verification_results['format_issues'].append(f"读取文件 {file_path} 失败: {e}")
            
            self.verification_results['total_output_articles'] = total_articles
            self.verification_results['field_completeness'] = field_stats
            
            print(f"✅ 输出统计: {len(json_files)} 个文件，{total_articles} 篇文章")
            return True
            
        except Exception as e:
            print(f"❌ 输出验证失败: {e}")
            return False
    
    def verify_data_integrity(self) -> bool:
        """
        验证数据完整性
        
        Returns:
            bool: 数据是否完整
        """
        print("\n🔍 验证数据完整性...")
        
        # 检查文章总数
        if self.verification_results['total_input_articles'] != self.verification_results['total_output_articles']:
            print(f"❌ 文章数量不匹配: 输入 {self.verification_results['total_input_articles']} vs 输出 {self.verification_results['total_output_articles']}")
            self.verification_results['data_integrity'] = False
        else:
            print(f"✅ 文章数量匹配: {self.verification_results['total_input_articles']} 篇")
        
        # 检查字段完整性
        print("\n🔍 验证字段完整性...")
        for field, stats in self.verification_results['field_completeness'].items():
            total = self.verification_results['total_output_articles']
            present_rate = (stats['present'] / total * 100) if total > 0 else 0
            missing_rate = ((stats['missing'] + stats['null']) / total * 100) if total > 0 else 0
            
            print(f"  {field}: {present_rate:.1f}% 完整 ({stats['present']}/{total})")
            
            if missing_rate > 10:  # 超过10%缺失
                print(f"    ⚠️  缺失率较高: {missing_rate:.1f}%")
        
        # 检查时间戳转换
        print("\n🔍 验证时间戳转换...")
        valid_ts = self.verification_results['timestamp_validation']['valid']
        invalid_ts = self.verification_results['timestamp_validation']['invalid']
        total_ts = valid_ts + invalid_ts
        
        if total_ts > 0:
            valid_rate = (valid_ts / total_ts * 100)
            print(f"  有效时间戳: {valid_rate:.1f}% ({valid_ts}/{total_ts})")
            
            if invalid_ts > 0:
                print(f"  ⚠️  无效时间戳: {invalid_ts} 个")
        
        return self.verification_results['data_integrity']
    
    def verify_date_organization(self) -> bool:
        """
        验证日期组织
        
        Returns:
            bool: 日期组织是否正确
        """
        print("\n🔍 验证日期组织...")
        
        # 检查日期范围
        dates = sorted(self.verification_results['date_coverage'].keys())
        if dates:
            print(f"📅 日期范围: {dates[0]} 到 {dates[-1]}")
            print(f"📊 日期数量: {len(dates)} 天")
            
            # 检查是否有空的日期文件
            empty_dates = [date for date, count in self.verification_results['date_coverage'].items() if count == 0]
            if empty_dates:
                print(f"⚠️  空日期文件: {empty_dates}")
            
            return True
        else:
            print("❌ 未找到有效的日期文件")
            return False
    
    def print_verification_report(self):
        """打印验证报告"""
        print("\n" + "=" * 60)
        print("Finnhub News 数据处理验证报告")
        print("=" * 60)
        
        print(f"输入目录: {self.input_dir}")
        print(f"输出目录: {self.output_dir}")
        print(f"输入文件数: {self.verification_results['input_files']}")
        print(f"输出文件数: {self.verification_results['output_files']}")
        print(f"输入文章数: {self.verification_results['total_input_articles']}")
        print(f"输出文章数: {self.verification_results['total_output_articles']}")
        
        # 字段完整性统计
        if self.verification_results['field_completeness']:
            print(f"\n字段完整性统计:")
            for field, stats in self.verification_results['field_completeness'].items():
                total = self.verification_results['total_output_articles']
                present_rate = (stats['present'] / total * 100) if total > 0 else 0
                print(f"  {field}: {present_rate:.1f}% ({stats['present']}/{total})")
        
        # 时间戳验证统计
        valid_ts = self.verification_results['timestamp_validation']['valid']
        invalid_ts = self.verification_results['timestamp_validation']['invalid']
        total_ts = valid_ts + invalid_ts
        if total_ts > 0:
            print(f"\n时间戳验证:")
            print(f"  有效时间戳: {valid_ts}/{total_ts} ({valid_ts/total_ts*100:.1f}%)")
            if invalid_ts > 0:
                print(f"  无效时间戳: {invalid_ts}")
        
        # 日期分布
        if self.verification_results['date_coverage']:
            print(f"\n日期分布 (前10个):")
            sorted_dates = sorted(self.verification_results['date_coverage'].keys())
            for date_str in sorted_dates[:10]:
                count = self.verification_results['date_coverage'][date_str]
                print(f"  {date_str}: {count} 篇")
            if len(sorted_dates) > 10:
                print(f"  ... 还有 {len(sorted_dates) - 10} 个日期")
        
        # 问题报告
        if self.verification_results['format_issues']:
            print(f"\n⚠️  格式问题 ({len(self.verification_results['format_issues'])} 个):")
            for issue in self.verification_results['format_issues']:
                print(f"  • {issue}")
        
        if self.verification_results['processing_errors']:
            print(f"\n❌ 处理错误 ({len(self.verification_results['processing_errors'])} 个):")
            for error in self.verification_results['processing_errors']:
                print(f"  • {error}")
        
        # 总体状态
        overall_status = "✅ 通过" if self.verification_results['data_integrity'] else "❌ 失败"
        print(f"\n总体验证状态: {overall_status}")
        print("=" * 60)
    
    def run_verification(self) -> bool:
        """
        运行完整验证
        
        Returns:
            bool: 验证是否通过
        """
        print("开始验证 Finnhub News 数据处理结果...")
        
        # 统计输入文章
        if not self.count_input_articles():
            return False
        
        # 验证输出文件
        if not self.verify_output_files():
            return False
        
        # 验证数据完整性
        integrity_ok = self.verify_data_integrity()
        
        # 验证日期组织
        date_ok = self.verify_date_organization()
        
        # 打印报告
        self.print_verification_report()
        
        return integrity_ok and date_ok


def main():
    """主函数"""
    verifier = FinnhubVerifier()
    
    success = verifier.run_verification()
    
    if success:
        print("\n🎉 验证通过！")
        print("📊 Finnhub News 数据处理成功")
        print("📁 所有文件格式正确，数据完整")
        print("⏰ Unix 时间戳转换正确")
    else:
        print("\n❌ 验证失败！")
        print("🔧 请检查上述问题")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
